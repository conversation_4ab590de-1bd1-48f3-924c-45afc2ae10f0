[tool.poetry]
name = "polaris-api"
version = "1.0.0"
description = "Polaris API - Collaborative workspace backend"
authors = ["Polaris Team"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.103.0"
uvicorn = {extras = ["standard"], version = "^0.23.0"}
pydantic = {extras = ["email"], version = "^2.3.0"}
pydantic-settings = "^2.0.3"
sqlalchemy = "^2.0.20"
alembic = "^1.12.0"
asyncpg = "^0.28.0"
psycopg2-binary = "^2.9.7"
redis = "^4.6.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
httpx = "^0.24.1"
websockets = "^11.0.3"
python-socketio = "^5.8.0"
celery = "^5.3.1"
prometheus-client = "^0.17.1"
structlog = "^23.1.0"
sentry-sdk = {extras = ["fastapi"], version = "^1.32.0"}

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.1"
pre-commit = "^3.3.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto"
