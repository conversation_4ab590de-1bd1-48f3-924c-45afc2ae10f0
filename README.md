# Polaris - Collaborative Workspace

A production-grade, full-stack collaboration platform combining the best of Notion and Airtable. Built with React, TypeScript, FastAPI, and PostgreSQL.

## 🚀 Features

- **Rich Text Pages**: Create and edit documents with real-time collaboration
- **Dynamic Tables**: Spreadsheet-like tables with custom schemas and data types
- **Interactive Dashboards**: Drag-and-drop widgets and data visualization
- **Real-time Collaboration**: Live cursors, presence, and concurrent editing
- **Multi-tenant Workspaces**: Team-based organization with role-based access
- **Offline Support**: PWA with offline editing and sync
- **OAuth Authentication**: Google and GitHub integration
- **Component Library**: Reusable UI components with Storybook

## 🏗️ Architecture

### Monorepo Structure

```
polaris/
├── apps/
│   ├── web/              # Main React application
│   ├── editor/           # Rich text editor (micro-frontend)
│   └── dashboard/        # Dashboard builder (micro-frontend)
├── packages/
│   ├── ui/               # Shared component library
│   ├── shared-types/     # TypeScript types (Zod schemas)
│   └── utils/            # Shared utilities
├── services/
│   ├── api/              # FastAPI backend
│   ├── jobs/             # Background workers
│   └── realtime/         # WebSocket server
└── infra/
    ├── docker/           # Docker configurations
    └── scripts/          # Database and deployment scripts
```

### Tech Stack

**Frontend:**
- React 18 + TypeScript
- Vite (build tool)
- TailwindCSS (styling)
- Y.js (CRDT for real-time collaboration)
- Zustand (state management)
- React Query (data fetching)
- Storybook (component development)

**Backend:**
- FastAPI (Python)
- PostgreSQL (database)
- Redis (caching/pub-sub)
- SQLAlchemy (ORM)
- Alembic (migrations)
- WebSockets (real-time)

**DevOps:**
- Docker & docker-compose
- Turbo (monorepo management)
- pnpm (package management)
- GitHub Actions (CI/CD)

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- Python 3.11+
- Docker & docker-compose
- pnpm 8+

### Quick Start

1. **Clone and setup:**
   ```bash
   git clone <repository-url>
   cd polaris
   cp .env.example .env
   ```

2. **Install dependencies:**
   ```bash
   pnpm install
   ```

3. **Start services:**
   ```bash
   docker-compose up -d db redis
   ```

4. **Run the application:**
   ```bash
   # Terminal 1: Start API
   cd services/api
   poetry install
   poetry run uvicorn main:app --reload

   # Terminal 2: Start frontend
   pnpm dev
   ```

5. **Access the application:**
   - Frontend: http://localhost:3000
   - API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

### Full Docker Setup

```bash
docker-compose up --build
```

## 📚 Development Guide

### Package Scripts

```bash
# Development
pnpm dev              # Start all apps in development mode
pnpm build            # Build all packages and apps
pnpm test             # Run all tests
pnpm lint             # Lint all packages
pnpm type-check       # TypeScript type checking

# Component Library
pnpm storybook        # Start Storybook
pnpm build-storybook  # Build Storybook

# Formatting
pnpm format           # Format all files
pnpm format:check     # Check formatting
```

### Working with the Monorepo

- Use `pnpm --filter <package>` to run commands in specific packages
- Shared types are automatically available across all packages
- UI components can be imported from `@polaris/ui`

### Database Migrations

```bash
cd services/api
poetry run alembic revision --autogenerate -m "Description"
poetry run alembic upgrade head
```

## 🧪 Testing

- **Unit Tests**: Jest/Vitest for components and utilities
- **Integration Tests**: API endpoint testing with pytest
- **E2E Tests**: Cypress/Playwright for user flows
- **Visual Tests**: Storybook with Chromatic

```bash
pnpm test              # Run all tests
pnpm test:ui           # Run tests with UI
pnpm test:e2e          # Run E2E tests
```

## 🚀 Deployment

### Production Build

```bash
pnpm build
docker-compose -f docker-compose.prod.yml up --build
```

### Environment Variables

Copy `.env.example` to `.env` and configure:

- Database connection
- OAuth credentials (Google/GitHub)
- JWT secrets
- Redis connection
- File storage (AWS S3)

## 📖 API Documentation

The API is fully documented with OpenAPI/Swagger:
- Development: http://localhost:8000/docs
- Interactive docs with request/response examples
- Auto-generated from Pydantic models

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🔗 Links

- [API Documentation](http://localhost:8000/docs)
- [Component Library](http://localhost:6006)
- [Project Specification](./SPEC.md)
