{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "storybook-static/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "storybook": {"cache": false, "persistent": true}, "build-storybook": {"dependsOn": ["^build"], "outputs": ["storybook-static/**"]}}}