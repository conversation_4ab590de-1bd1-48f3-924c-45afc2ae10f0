{"name": "polaris", "version": "1.0.0", "description": "A collaborative workspace (Notion + Airtable hybrid)", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "storybook": "pnpm --filter @polaris/ui storybook", "build-storybook": "pnpm --filter @polaris/ui build-storybook"}, "devDependencies": {"@turbo/gen": "^1.10.12", "turbo": "^1.10.12", "prettier": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "typescript": "^5.1.6"}, "packageManager": "pnpm@8.6.10", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}