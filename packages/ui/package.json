{"name": "@polaris/ui", "version": "1.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest", "clean": "rm -rf dist .turbo node_modules storybook-static"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@polaris/shared-types": "workspace:*", "@polaris/utils": "workspace:*", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "zod": "^3.22.2", "framer-motion": "^10.16.4"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.3", "jsdom": "^22.1.0", "@storybook/addon-essentials": "^7.4.0", "@storybook/addon-interactions": "^7.4.0", "@storybook/addon-links": "^7.4.0", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.0", "@storybook/react": "^7.4.0", "@storybook/react-vite": "^7.4.0", "@storybook/testing-library": "^0.2.0", "storybook": "^7.4.0", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "tailwindcss": "^3.3.3"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}