import { z } from 'zod'

export const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
})

export const RegisterRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().min(1),
})

export const TokenResponseSchema = z.object({
  access_token: z.string(),
  refresh_token: z.string(),
  token_type: z.literal('bearer'),
  expires_in: z.number(),
})

export const OAuthStartResponseSchema = z.object({
  authorization_url: z.string().url(),
  state: z.string(),
})

export type LoginRequest = z.infer<typeof LoginRequestSchema>
export type RegisterRequest = z.infer<typeof RegisterRequestSchema>
export type TokenResponse = z.infer<typeof TokenResponseSchema>
export type OAuthStartResponse = z.infer<typeof OAuthStartResponseSchema>

export type OAuthProvider = 'google' | 'github'
