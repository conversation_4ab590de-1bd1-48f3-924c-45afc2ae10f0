import { z } from 'zod'

export const ColumnTypeSchema = z.enum([
  'text',
  'number',
  'boolean',
  'date',
  'select',
  'multi_select',
  'url',
  'email',
  'phone',
  'file',
])

export const ColumnSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: ColumnTypeSchema,
  options: z.record(z.any()).optional(),
  required: z.boolean().default(false),
})

export const TableSchemaSchema = z.object({
  columns: z.array(ColumnSchema),
})

export const TableSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  name: z.string(),
  schema: TableSchemaSchema,
  created_by: z.string().uuid(),
  updated_by: z.string().uuid(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})

export const TableRowSchema = z.object({
  id: z.string().uuid(),
  table_id: z.string().uuid(),
  data: z.record(z.any()),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})

export const CreateTableRequestSchema = z.object({
  name: z.string().min(1).max(100),
  schema: TableSchemaSchema,
})

export const UpdateTableRequestSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  schema: TableSchemaSchema.optional(),
})

export const CreateRowRequestSchema = z.object({
  data: z.record(z.any()),
})

export const UpdateRowRequestSchema = z.object({
  data: z.record(z.any()),
})

export const BulkCreateRowsRequestSchema = z.object({
  rows: z.array(CreateRowRequestSchema),
})

export type ColumnType = z.infer<typeof ColumnTypeSchema>
export type Column = z.infer<typeof ColumnSchema>
export type TableSchemaType = z.infer<typeof TableSchemaSchema>
export type Table = z.infer<typeof TableSchema>
export type TableRow = z.infer<typeof TableRowSchema>
export type CreateTableRequest = z.infer<typeof CreateTableRequestSchema>
export type UpdateTableRequest = z.infer<typeof UpdateTableRequestSchema>
export type CreateRowRequest = z.infer<typeof CreateRowRequestSchema>
export type UpdateRowRequest = z.infer<typeof UpdateRowRequestSchema>
export type BulkCreateRowsRequest = z.infer<typeof BulkCreateRowsRequestSchema>
