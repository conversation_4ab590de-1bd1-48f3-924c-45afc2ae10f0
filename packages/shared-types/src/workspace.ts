import { z } from 'zod'

export const WorkspaceRoleSchema = z.enum(['owner', 'admin', 'editor', 'viewer'])

export const WorkspaceSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  slug: z.string(),
  owner_id: z.string().uuid(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})

export const WorkspaceMembershipSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  user_id: z.string().uuid(),
  role: WorkspaceRoleSchema,
  created_at: z.string().datetime(),
})

export const CreateWorkspaceRequestSchema = z.object({
  name: z.string().min(1).max(100),
  slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/),
})

export const UpdateWorkspaceRequestSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/).optional(),
})

export const InviteMemberRequestSchema = z.object({
  email: z.string().email(),
  role: WorkspaceRoleSchema,
})

export const UpdateMemberRoleRequestSchema = z.object({
  role: WorkspaceRoleSchema,
})

export type WorkspaceRole = z.infer<typeof WorkspaceRoleSchema>
export type Workspace = z.infer<typeof WorkspaceSchema>
export type WorkspaceMembership = z.infer<typeof WorkspaceMembershipSchema>
export type CreateWorkspaceRequest = z.infer<typeof CreateWorkspaceRequestSchema>
export type UpdateWorkspaceRequest = z.infer<typeof UpdateWorkspaceRequestSchema>
export type InviteMemberRequest = z.infer<typeof InviteMemberRequestSchema>
export type UpdateMemberRoleRequest = z.infer<typeof UpdateMemberRoleRequestSchema>
