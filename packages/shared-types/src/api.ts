import { z } from 'zod'

export const ApiErrorSchema = z.object({
  detail: z.string(),
  code: z.string().optional(),
  field: z.string().optional(),
})

export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
})

export const PaginatedResponseSchema = z.object({
  items: z.array(z.any()),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  pages: z.number(),
})

export const HealthCheckResponseSchema = z.object({
  status: z.literal('ok'),
  timestamp: z.string().datetime(),
  version: z.string(),
  database: z.boolean(),
})

export type ApiError = z.infer<typeof ApiErrorSchema>
export type Pagination = z.infer<typeof PaginationSchema>
export type PaginatedResponse<T = any> = Omit<z.infer<typeof PaginatedResponseSchema>, 'items'> & {
  items: T[]
}
export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>
