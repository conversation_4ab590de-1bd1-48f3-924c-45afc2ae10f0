import { z } from 'zod'

export const PageSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  title: z.string(),
  ydoc_id: z.string(),
  content_metadata: z.record(z.any()).optional(),
  created_by: z.string().uuid(),
  updated_by: z.string().uuid(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})

export const CreatePageRequestSchema = z.object({
  title: z.string().min(1).max(200),
  content_metadata: z.record(z.any()).optional(),
})

export const UpdatePageRequestSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content_metadata: z.record(z.any()).optional(),
})

export type Page = z.infer<typeof PageSchema>
export type CreatePageRequest = z.infer<typeof CreatePageRequestSchema>
export type UpdatePageRequest = z.infer<typeof UpdatePageRequestSchema>
