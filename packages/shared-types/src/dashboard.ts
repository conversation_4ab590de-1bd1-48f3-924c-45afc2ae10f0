import { z } from 'zod'

export const WidgetTypeSchema = z.enum([
  'chart',
  'table',
  'metric',
  'text',
])

export const ChartTypeSchema = z.enum([
  'bar',
  'line',
  'pie',
  'doughnut',
  'area',
  'scatter',
])

export const WidgetConfigSchema = z.object({
  type: WidgetTypeSchema,
  title: z.string(),
  position: z.object({
    x: z.number(),
    y: z.number(),
    width: z.number(),
    height: z.number(),
  }),
  config: z.record(z.any()),
})

export const DashboardSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  name: z.string(),
  widgets: z.array(WidgetConfigSchema),
  created_by: z.string().uuid(),
  updated_by: z.string().uuid(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
})

export const CreateDashboardRequestSchema = z.object({
  name: z.string().min(1).max(100),
  widgets: z.array(WidgetConfigSchema).default([]),
})

export const UpdateDashboardRequestSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  widgets: z.array(WidgetConfigSchema).optional(),
})

export const CreateWidgetRequestSchema = z.object({
  widget: WidgetConfigSchema,
})

export type WidgetType = z.infer<typeof WidgetTypeSchema>
export type ChartType = z.infer<typeof ChartTypeSchema>
export type WidgetConfig = z.infer<typeof WidgetConfigSchema>
export type Dashboard = z.infer<typeof DashboardSchema>
export type CreateDashboardRequest = z.infer<typeof CreateDashboardRequestSchema>
export type UpdateDashboardRequest = z.infer<typeof UpdateDashboardRequestSchema>
export type CreateWidgetRequest = z.infer<typeof CreateWidgetRequestSchema>
