{"name": "@polaris/shared-types", "version": "1.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist .turbo node_modules"}, "dependencies": {"zod": "^3.22.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "typescript": "^5.0.2"}}