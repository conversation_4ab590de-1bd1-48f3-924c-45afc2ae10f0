import { Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'

import { AuthProvider } from './contexts/AuthContext'
import { ProtectedRoute } from './components/auth/ProtectedRoute'
import { Layout } from './components/layout/Layout'

// Pages
import { HomePage } from './pages/HomePage'
import { LoginPage } from './pages/auth/LoginPage'
import { RegisterPage } from './pages/auth/RegisterPage'
import { DashboardPage } from './pages/DashboardPage'
import { WorkspacePage } from './pages/workspace/WorkspacePage'
import { PageEditor } from './pages/editor/PageEditor'
import { TableView } from './pages/table/TableView'
import { NotFoundPage } from './pages/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          
          {/* Protected routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/workspace/:workspaceId"
            element={
              <ProtectedRoute>
                <Layout>
                  <WorkspacePage />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/workspace/:workspaceId/page/:pageId"
            element={
              <ProtectedRoute>
                <Layout>
                  <PageEditor />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/workspace/:workspaceId/table/:tableId"
            element={
              <ProtectedRoute>
                <Layout>
                  <TableView />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* 404 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
        
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </div>
    </AuthProvider>
  )
}

export default App
