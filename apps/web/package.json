{"name": "@polaris/web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "clean": "rm -rf dist .turbo node_modules"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "@polaris/ui": "workspace:*", "@polaris/shared-types": "workspace:*", "@polaris/utils": "workspace:*", "yjs": "^13.6.7", "y-websocket": "^1.5.0", "y-prosemirror": "^1.2.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.31.7", "prosemirror-model": "^1.19.3", "prosemirror-schema-basic": "^1.2.2", "prosemirror-schema-list": "^1.3.0", "prosemirror-keymap": "^1.2.2", "prosemirror-history": "^1.3.2", "prosemirror-commands": "^1.5.2", "@tanstack/react-query": "^4.32.6", "axios": "^1.5.0", "zustand": "^4.4.1", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "zod": "^3.22.2", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.3", "jsdom": "^22.1.0", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "tailwindcss": "^3.3.3"}}